# Frontend Development Guide

This guide explains how to run both frontend interfaces side by side for development.

## 🎯 Available Frontends

### Assistants Web (Enterprise)
- **Path**: `src/interfaces/assistants_web`
- **Port**: `4000`
- **Script**: `./scripts/frontend-assistant.sh`
- **URL**: http://localhost:4000
- **Features**: Workspace management, enterprise features

### Coral Web (Personal)
- **Path**: `src/interfaces/coral_web`
- **Port**: `3000`
- **Script**: `./scripts/frontend-coral.sh`
- **URL**: http://localhost:3000
- **Features**: Personal use, single-user system

## 🚀 Running Frontends

### Option 1: Run Both Frontends Simultaneously
```bash
# Terminal 1: Start Assistants Web
./scripts/frontend-assistant.sh

# Terminal 2: Start Coral Web
./scripts/frontend-coral.sh
```

### Option 2: Run Individual Frontends
```bash
# Assistants Web only (port 4000)
./scripts/frontend-assistant.sh

# Coral Web only (port 3000)
./scripts/frontend-coral.sh
```

### Option 3: Use NPM Scripts
```bash
# Assistants Web
npm run frontend:assistant

# Coral Web
npm run frontend:coral
```

### Option 4: Combined Development (Backend + Assistants Web)
```bash
# Starts backend + assistants_web frontend
./scripts/dev.sh
```

## 🔧 Development Features

- **Hot Reload**: Both frontends support hot module replacement
- **Independent Development**: Work on both interfaces simultaneously
- **Shared Backend**: Both frontends connect to the same backend (port 8000)
- **Different Ports**: No conflicts when running both

## 📁 Project Structure

```
cohere-toolkit/
├── scripts/
│   ├── dev.sh                 # Backend + Assistants Web
│   ├── backend-dev.sh         # Backend only
│   ├── frontend-assistant.sh  # Assistants Web (port 4000)
│   └── frontend-coral.sh      # Coral Web (port 3000)
├── src/interfaces/
│   ├── assistants_web/        # Enterprise frontend
│   └── coral_web/             # Personal frontend
```

## 🌐 Access URLs

When running both frontends:
- **Assistants Web**: http://localhost:4000
- **Coral Web**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 💡 Tips

1. **Theme Development**: You can now test theme changes across both interfaces
2. **Feature Comparison**: Compare enterprise vs personal features side by side
3. **Cross-Interface Testing**: Ensure changes work in both environments
4. **Port Management**: Each frontend runs on a different port to avoid conflicts

## 🛠 Troubleshooting

- **Port Conflicts**: If ports are in use, the scripts will show an error
- **Backend Required**: Both frontends need the backend running on port 8000
- **Dependencies**: Run `npm install` in each frontend directory if needed
