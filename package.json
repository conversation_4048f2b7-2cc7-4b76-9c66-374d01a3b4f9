{"name": "cohere-toolkit", "version": "1.0.0", "description": "Cohere Toolkit - Local Development Scripts", "scripts": {"dev": "./scripts/dev.sh", "backend:dev": "./scripts/backend-dev.sh", "frontend:assistant": "./scripts/frontend-assistant.sh", "frontend:coral": "./scripts/frontend-coral.sh", "db:start": "docker-compose up db redis -d", "db:stop": "docker-compose stop db redis", "db:logs": "docker-compose logs -f db redis", "setup": "poetry install --with dev && cd src/interfaces/coral_web && npm install", "migrate": "poetry run alembic -c src/backend/alembic.ini upgrade head", "test": "poetry run pytest src/backend/tests/unit/ --cov=src/backend --cov-report=xml", "lint": "poetry run ruff check", "lint:fix": "poetry run ruff check --fix", "format": "poetry run ruff format"}, "keywords": ["cohere", "ai", "toolkit", "<PERSON><PERSON><PERSON>", "nextjs"], "author": "Cohere", "license": "MIT", "engines": {"node": ">=20.12.2", "npm": ">=9"}}