#!/bin/bash

# Backend Development Script
# This script runs the FastAPI backend in Docker with hot reload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Cohere Toolkit Backend (Docker with Hot Reload)${NC}"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Error: pyproject.toml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Load environment variables from .env.local if it exists
if [ -f ".env.local" ]; then
    echo -e "${GREEN}📄 Loading environment variables from .env.local${NC}"
    export $(grep -v '^#' .env.local | xargs)
else
    echo -e "${YELLOW}⚠️  Warning: .env.local not found. Using default environment variables.${NC}"
fi

# Check if Dock<PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Error: Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Start the backend with database services
echo -e "${GREEN}✅ Starting backend and database services with hot reload...${NC}"
echo -e "${BLUE}   Backend will be available at: http://localhost:8000${NC}"
echo -e "${BLUE}   API docs will be available at: http://localhost:8000/docs${NC}"
echo -e "${YELLOW}   Press Ctrl+C to stop the server${NC}"

# Start backend with hot reload
docker-compose up backend db redis
