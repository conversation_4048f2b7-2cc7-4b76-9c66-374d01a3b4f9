#!/bin/bash

# Combined Development Script
# This script runs both backend and frontend in parallel for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Cohere Toolkit (Local Development)${NC}"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Error: pyproject.toml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo -e "\n${YELLOW}🛑 Stopping development servers...${NC}"
    jobs -p | xargs -r kill
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Check if database services are running
echo -e "${BLUE}🔍 Checking database services...${NC}"
if ! nc -z localhost 5432 2>/dev/null; then
    echo -e "${YELLOW}📦 Starting database services...${NC}"
    docker-compose up db redis -d
    echo -e "${BLUE}⏳ Waiting for database to be ready...${NC}"
    sleep 5
fi

# Load nvm if available
if [ -f ~/.nvm/nvm.sh ]; then
    source ~/.nvm/nvm.sh
fi

# Start backend in background
echo -e "${GREEN}🔧 Starting backend server...${NC}"
./scripts/backend-dev.sh &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 10

# Start frontend in background
echo -e "${GREEN}🎨 Starting frontend server...${NC}"
./scripts/frontend-dev.sh &
FRONTEND_PID=$!

# Display information
echo -e "\n${GREEN}✅ Development servers started!${NC}"
echo -e "${BLUE}   Backend: http://localhost:8000${NC}"
echo -e "${BLUE}   Frontend: http://localhost:4000${NC}"
echo -e "${BLUE}   API Docs: http://localhost:8000/docs${NC}"
echo -e "\n${YELLOW}   Press Ctrl+C to stop all servers${NC}"

# Wait for background processes
wait
