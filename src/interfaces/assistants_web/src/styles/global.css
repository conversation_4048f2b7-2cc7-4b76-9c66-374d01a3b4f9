@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    height: 100%;
    width: 100%;
    @apply selection:bg-marble-950 dark:selection:bg-marble-950;

    /* Firefox */
    scrollbar-color: theme('colors.marble.800') transparent;
  }

  ::-webkit-scrollbar {
    @apply h-2.5 w-2.5;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 5px 5px rgb(189 189 189 / 0.5); /* marble-500/50 */
    @apply rounded-b-full rounded-t-full border-2 border-solid border-transparent;
  }
}

@layer utilities {
  /* @see: https://css-tricks.com/scrollbars-on-hover/ */
  .hide-scrollbar {
    overflow-y: scroll !important;
    mask-position: left bottom;
    mask-size: 100% 20000px;
    mask-image: linear-gradient(to top, transparent, black),
      linear-gradient(to left, transparent 12px, black 12px);
    transition: mask-position 300ms ease;
    transition-duration: 300ms;
  }

  .hide-scrollbar:hover,
  .hide-scrollbar:focus {
    mask-position: left top;
  }
  .font-cuts-0 {
    font-variation-settings: 'cuts' 0, 'move' 0;
  }

  .font-cuts-1 {
    font-variation-settings: 'cuts' 100, 'move' 100;
    font-variant-ligatures: 'common-ligatures';
  }

  .font-cuts-2 {
    font-variation-settings: 'cuts' 200, 'move' 200;
    font-variant-ligatures: 'common-ligatures';
  }

  /* Simple flash effect for theme transitions */
  .theme-transition-flash {
    transition: opacity 0.15s ease-in-out;
    opacity: 0.85;
  }

  .font-cuts-3 {
    font-variation-settings: 'cuts' 300, 'move' 300;
    font-variant-ligatures: 'common-ligatures';
  }
}

/* hide recaptcha badge */
.grecaptcha-badge {
  visibility: hidden;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: theme('colors.marble.1000');
  font-family: theme('fontFamily.body');
  font-size: 16px;
  font-weight: 400;
}

/* Google File Picker styles */
.picker-dialog {
  @apply rounded-lg border !border-marble-800 drop-shadow-2xl;
}
