/**
 * Simple flash effect for theme transitions
 * Provides a quick, subtle flash when switching themes instead of complex animations
 */
export const applyThemeWithFlash = (setTheme: (theme: string) => void, theme: string) => {
  // Apply a quick opacity flash effect
  document.body.style.transition = 'opacity 0.1s ease-in-out';
  document.body.style.opacity = '0.8';
  
  setTimeout(() => {
    setTheme(theme);
    document.body.style.opacity = '1';
    setTimeout(() => {
      // Clean up the transition style
      document.body.style.transition = '';
    }, 100);
  }, 50);
};
