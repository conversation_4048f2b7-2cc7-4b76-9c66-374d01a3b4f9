/**
 * Simple flash effect for theme transitions
 * Provides a quick, subtle flash when switching themes instead of complex animations
 */
export const applyThemeWithFlash = (setTheme: (theme: string) => void, theme: string) => {
  // Apply the theme change immediately to avoid React hook issues
  setTheme(theme);

  // Add a simple flash effect using CSS classes
  document.body.classList.add('theme-transition-flash');

  // Remove the flash class after a short duration
  setTimeout(() => {
    document.body.classList.remove('theme-transition-flash');
  }, 150);
};
