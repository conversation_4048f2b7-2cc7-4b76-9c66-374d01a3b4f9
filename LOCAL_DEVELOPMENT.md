# Local Development Setup

This guide shows you how to run the Cohere Toolkit locally with hot reload for faster development, using Docker only for database services.

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** with [Poetry](https://python-poetry.org/docs/#installation)
- **Node.js 20.12.2+** with npm
- **Docker** (for database services)

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd cohere-toolkit

# Install dependencies
npm run setup
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.local .env

# Edit .env and update your Cohere API key
# COHERE_API_KEY=your-actual-api-key-here
```

### 3. Start Development

```bash
# Start everything (databases + backend + frontend)
npm run dev

# Or start services individually:
npm run db:start        # Start databases only
npm run backend:dev      # Start backend only  
npm run frontend:dev     # Start frontend only
```

### 4. Access the Application

- **Frontend**: http://localhost:4000 (Assistants Web - Enterprise)
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📁 Project Structure

```
cohere-toolkit/
├── scripts/
│   ├── dev.sh                 # Combined development script
│   ├── backend-dev.sh         # Backend development script
│   ├── frontend-assistant.sh  # Assistants Web frontend script
│   └── frontend-coral.sh      # Coral Web frontend script
├── src/
│   ├── backend/            # FastAPI backend
│   └── interfaces/
│       ├── coral_web/      # Next.js frontend (personal)
│       └── assistants_web/ # Next.js frontend (enterprise)
├── .env.local              # Environment variables template
├── package.json            # Development scripts
└── LOCAL_DEVELOPMENT.md    # This file
```

## 🛠 Available Scripts

### NPM Scripts

```bash
npm run dev              # Start all services (databases + backend + frontend)
npm run backend:dev      # Start backend only with hot reload
npm run frontend:dev     # Start frontend only with hot reload
npm run db:start         # Start database services (PostgreSQL + Redis)
npm run db:stop          # Stop database services
npm run db:logs          # View database logs
npm run setup            # Install all dependencies
npm run migrate          # Run database migrations
npm run test             # Run backend tests
npm run lint             # Check code style
npm run lint:fix         # Fix code style issues
npm run format           # Format code
```

### Direct Script Usage

```bash
./scripts/dev.sh                # Start all services (backend + assistants_web)
./scripts/backend-dev.sh        # Start backend only
./scripts/frontend-assistant.sh # Start assistants_web frontend only (port 4000)
./scripts/frontend-coral.sh     # Start coral_web frontend only (port 3000)
```

## 🔧 Development Workflow

### Hot Reload

- **Backend**: Uses `uvicorn --reload` for automatic restart on file changes
- **Frontend**: Uses Next.js dev server with hot module replacement

### Database Management

```bash
# Start databases
npm run db:start

# Run migrations
npm run migrate

# View database logs
npm run db:logs

# Stop databases
npm run db:stop
```

### Environment Variables

The `.env.local` file contains all necessary environment variables:

```bash
# Required
COHERE_API_KEY=your-api-key-here

# Database (automatically configured for local Docker)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres
REDIS_URL=redis://localhost:6379

# Development settings
ENVIRONMENT=development
DEBUG=true
ALLOWED_ORIGINS=http://localhost:4000,http://127.0.0.1:4000
```

## 🐛 Troubleshooting

### Backend Issues

```bash
# Check if dependencies are installed
poetry install --with dev

# Check if database is running
nc -z localhost 5432

# Check backend logs
# Backend logs appear in the terminal where you ran the script
```

### Frontend Issues

```bash
# Check if dependencies are installed
cd src/interfaces/coral_web && npm install

# Check if backend is running
curl http://localhost:8000/health

# Clear Next.js cache
cd src/interfaces/coral_web && rm -rf .next
```

### Database Issues

```bash
# Restart database services
npm run db:stop && npm run db:start

# Check database logs
npm run db:logs

# Reset database (WARNING: This will delete all data)
docker-compose down -v && npm run db:start
```

## 🔄 Switching Between Docker and Local

### From Docker to Local Development

```bash
# Stop Docker services
docker-compose down

# Start local development
npm run dev
```

### From Local to Docker Development

```bash
# Stop local services (Ctrl+C in terminals)
# Start Docker services
docker-compose up
```

## 📝 Notes

- **Database**: Always runs in Docker for consistency
- **Backend**: Runs locally with Poetry for faster development
- **Frontend**: Runs locally with npm for hot module replacement (currently using `assistants_web` - enterprise version)
- **Hot Reload**: Both backend and frontend automatically reload on changes
- **API Key**: Make sure to set your actual Cohere API key in `.env`
- **Frontend Choice**: The setup uses `assistants_web` (enterprise) by default. Use `./scripts/frontend-coral.sh` to run `coral_web` (personal) on port 3000, or run both frontends simultaneously

## 🆘 Getting Help

If you encounter issues:

1. Check that all prerequisites are installed
2. Verify your `.env` file has the correct API key
3. Ensure database services are running: `npm run db:start`
4. Check the troubleshooting section above
5. Look at the logs in the terminal where you started the services
