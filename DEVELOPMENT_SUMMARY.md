# Development Setup Summary

This document summarizes the local development setup for Cohere Toolkit.

## 🚀 Quick Commands

### Start Development

```bash
# Recommended: Backend + Assistants Web (Enterprise)
npm run dev

# All services: Backend + Both Frontends
npm run dev:all

# Individual services
npm run backend:dev           # Backend only
npm run frontend:assistant    # Assistants Web (port 4000)
npm run frontend:coral        # Coral Web (port 3000)
```

### Database Management

```bash
npm run db:start    # Start PostgreSQL + Redis
npm run db:stop     # Stop databases
npm run db:logs     # View database logs
npm run migrate     # Run database migrations
```

### Setup & Maintenance

```bash
npm run setup              # Install all dependencies
npm run setup:backend      # Backend dependencies only
npm run setup:frontend     # Frontend dependencies only
```

## 🌐 Access URLs

- **Assistants Web (Enterprise)**: http://localhost:4000
- **Coral Web (Personal)**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📋 Prerequisites

- **Python 3.11** with Poetry
- **Node.js 20.12.2+** with npm
- **Docker** (for database services)

## 🔧 Architecture

- **Backend**: Runs locally with Poetry for fast development and debugging
- **Frontend**: Both interfaces run locally with npm for hot reload
- **Database**: PostgreSQL and Redis run in Docker containers
- **Authentication**: Basic authentication enabled by default

## 📁 Key Files

- `package.json` - NPM scripts for development
- `LOCAL_DEVELOPMENT.md` - Detailed development guide
- `scripts/` - Individual development scripts
- `src/backend/config/configuration.yaml` - Backend configuration
- `.env` or `.env.local` - Environment variables

## 🔑 Environment Setup

1. Copy environment template: `cp .env.local .env`
2. Edit `.env` and set your Cohere API key: `COHERE_API_KEY=your-key-here`
3. Run setup: `npm run setup`
4. Start development: `npm run dev`

## 🛠 Development Features

- ✅ Hot reload for backend and frontend
- ✅ Basic authentication enabled
- ✅ Both enterprise and personal frontends available
- ✅ Database migrations handled automatically
- ✅ Comprehensive error checking and logging
- ✅ Easy switching between development modes
